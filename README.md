# FantomUI - Modern 3D Website

A cutting-edge 3D website built with React Three Fiber, showcasing modern web technologies and immersive 3D experiences.

## 🚀 Features

- **Interactive 3D Scene** - Floating geometric shapes with smooth animations
- **Modern UI/UX** - Glassmorphism design with smooth transitions
- **Responsive Design** - Optimized for all devices and screen sizes
- **Performance Optimized** - Hardware-accelerated WebGL rendering
- **Smooth Animations** - Framer Motion for fluid UI animations
- **Particle Effects** - Dynamic particle field background
- **TypeScript** - Full type safety and better developer experience

## 🛠️ Tech Stack

- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe development
- **Three.js** - 3D graphics library
- **React Three Fiber** - React renderer for Three.js
- **React Three Drei** - Useful helpers and abstractions
- **Framer Motion** - Animation library
- **Tailwind CSS** - Utility-first CSS framework
- **Vite** - Fast build tool and dev server

## 📦 Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:3000`

## 🏗️ Build for Production

```bash
npm run build
```

## 🎨 Customization

### Adding New 3D Objects

Create new components in `src/components/` and add them to the `Scene3D.tsx` file:

```tsx
import YourNewComponent from './YourNewComponent'

// In Scene3D component
<Float speed={2} rotationIntensity={1} floatIntensity={2}>
  <YourNewComponent />
</Float>
```

### Modifying Colors and Themes

Update the color scheme in `tailwind.config.js` and the 3D object materials in component files.

### Performance Optimization

- Adjust particle count in `ParticleField.tsx`
- Modify LOD (Level of Detail) for complex geometries
- Use `useMemo` for expensive calculations

## 📱 Browser Support

- Chrome 80+
- Firefox 78+
- Safari 14+
- Edge 80+

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- [Three.js](https://threejs.org/) - Amazing 3D library
- [React Three Fiber](https://docs.pmnd.rs/react-three-fiber) - React renderer for Three.js
- [Framer Motion](https://www.framer.com/motion/) - Animation library
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
