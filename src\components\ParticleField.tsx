import React, { useRef, useMemo } from 'react'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'

const ParticleField: React.FC = () => {
  const pointsRef = useRef<THREE.Points>(null)
  
  const particleCount = 1000
  
  const { positions, colors } = useMemo(() => {
    const positions = new Float32Array(particleCount * 3)
    const colors = new Float32Array(particleCount * 3)
    
    for (let i = 0; i < particleCount; i++) {
      // Random positions in a sphere
      const radius = Math.random() * 50 + 10
      const theta = Math.random() * Math.PI * 2
      const phi = Math.acos(Math.random() * 2 - 1)
      
      positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta)
      positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta)
      positions[i * 3 + 2] = radius * Math.cos(phi)
      
      // Random colors with blue/purple theme
      const colorChoice = Math.random()
      if (colorChoice < 0.33) {
        colors[i * 3] = 0.23 // Blue
        colors[i * 3 + 1] = 0.51
        colors[i * 3 + 2] = 0.96
      } else if (colorChoice < 0.66) {
        colors[i * 3] = 0.55 // Purple
        colors[i * 3 + 1] = 0.36
        colors[i * 3 + 2] = 0.96
      } else {
        colors[i * 3] = 0.02 // Cyan
        colors[i * 3 + 1] = 0.71
        colors[i * 3 + 2] = 0.83
      }
    }
    
    return { positions, colors }
  }, [])

  useFrame((state) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.05
      pointsRef.current.rotation.x = state.clock.elapsedTime * 0.02
    }
  })

  return (
    <points ref={pointsRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-color"
          count={particleCount}
          array={colors}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.1}
        vertexColors
        transparent
        opacity={0.6}
        sizeAttenuation
        blending={THREE.AdditiveBlending}
      />
    </points>
  )
}

export default ParticleField
