import React from 'react'
import { Canvas } from '@react-three/fiber'
import { Suspense } from 'react'
import Navigation from './components/Navigation'
import Hero from './components/Hero'
import Scene3D from './components/Scene3D'
import { motion } from 'framer-motion'

function App() {
  return (
    <div className="relative min-h-screen">
      {/* Navigation */}
      <Navigation />
      
      {/* 3D Background Scene */}
      <div className="fixed inset-0 z-0">
        <Canvas
          camera={{ position: [0, 0, 5], fov: 75 }}
          gl={{ antialias: true, alpha: true }}
        >
          <Suspense fallback={null}>
            <Scene3D />
          </Suspense>
        </Canvas>
      </div>

      {/* Content */}
      <div className="relative z-10">
        <Hero />
        
        {/* Features Section */}
        <section className="min-h-screen flex items-center justify-center px-6">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto text-center"
          >
            <h2 className="text-4xl md:text-6xl font-bold mb-8 gradient-text">
              Modern 3D Experience
            </h2>
            <p className="text-xl md:text-2xl text-gray-300 mb-12">
              Built with cutting-edge web technologies for the future
            </p>
            
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  title: "React Three Fiber",
                  description: "Declarative 3D graphics with React components"
                },
                {
                  title: "WebGL Performance",
                  description: "Hardware-accelerated 3D rendering in the browser"
                },
                {
                  title: "Responsive Design",
                  description: "Seamless experience across all devices"
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className="glass rounded-xl p-6 hover:glow-blue transition-all duration-300"
                >
                  <h3 className="text-xl font-semibold mb-4 text-blue-400">
                    {feature.title}
                  </h3>
                  <p className="text-gray-300">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </section>

        {/* Contact Section */}
        <section className="min-h-screen flex items-center justify-center px-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            className="max-w-2xl mx-auto text-center"
          >
            <h2 className="text-4xl md:text-6xl font-bold mb-8">
              Ready to Build?
            </h2>
            <p className="text-xl text-gray-300 mb-12">
              Let's create something amazing together with modern 3D web technologies
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="glass rounded-full px-8 py-4 text-lg font-semibold hover:glow-purple transition-all duration-300"
            >
              Get Started
            </motion.button>
          </motion.div>
        </section>
      </div>
    </div>
  )
}

export default App
