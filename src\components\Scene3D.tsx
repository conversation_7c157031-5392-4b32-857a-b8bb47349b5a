import React, { useRef } from 'react'
import { useFrame } from '@react-three/fiber'
import { OrbitControls, Stars, Float } from '@react-three/drei'
import * as THREE from 'three'
import FloatingCube from './FloatingCube'
import ParticleField from './ParticleField'

const Scene3D: React.FC = () => {
  const groupRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.1
    }
  })

  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.3} />
      <pointLight position={[10, 10, 10]} intensity={1} color="#3b82f6" />
      <pointLight position={[-10, -10, -10]} intensity={0.5} color="#8b5cf6" />
      <directionalLight position={[0, 10, 5]} intensity={0.5} />

      {/* Background Stars */}
      <Stars
        radius={300}
        depth={60}
        count={1000}
        factor={7}
        saturation={0}
        fade
        speed={0.5}
      />

      {/* Particle Field */}
      <ParticleField />

      {/* Main 3D Objects Group */}
      <group ref={groupRef}>
        {/* Central Floating Cubes */}
        <Float speed={2} rotationIntensity={1} floatIntensity={2}>
          <FloatingCube position={[0, 0, 0]} color="#3b82f6" size={1} />
        </Float>
        
        <Float speed={1.5} rotationIntensity={0.5} floatIntensity={1}>
          <FloatingCube position={[3, 2, -2]} color="#8b5cf6" size={0.7} />
        </Float>
        
        <Float speed={2.5} rotationIntensity={1.5} floatIntensity={1.5}>
          <FloatingCube position={[-3, -1, 1]} color="#06b6d4" size={0.8} />
        </Float>

        <Float speed={1.8} rotationIntensity={0.8} floatIntensity={1.2}>
          <FloatingCube position={[2, -2, 2]} color="#ec4899" size={0.6} />
        </Float>

        {/* Geometric Shapes */}
        <Float speed={1} rotationIntensity={2} floatIntensity={0.5}>
          <mesh position={[-4, 3, -3]}>
            <octahedronGeometry args={[0.8]} />
            <meshStandardMaterial
              color="#10b981"
              emissive="#10b981"
              emissiveIntensity={0.2}
              transparent
              opacity={0.8}
            />
          </mesh>
        </Float>

        <Float speed={2.2} rotationIntensity={1.2} floatIntensity={1.8}>
          <mesh position={[4, -3, 1]}>
            <tetrahedronGeometry args={[1]} />
            <meshStandardMaterial
              color="#f59e0b"
              emissive="#f59e0b"
              emissiveIntensity={0.2}
              transparent
              opacity={0.7}
            />
          </mesh>
        </Float>

        {/* Torus Ring */}
        <Float speed={0.8} rotationIntensity={3} floatIntensity={0.3}>
          <mesh position={[0, 4, -4]} rotation={[Math.PI / 4, 0, 0]}>
            <torusGeometry args={[2, 0.3, 16, 100]} />
            <meshStandardMaterial
              color="#6366f1"
              emissive="#6366f1"
              emissiveIntensity={0.1}
              transparent
              opacity={0.6}
              wireframe
            />
          </mesh>
        </Float>
      </group>

      {/* Camera Controls */}
      <OrbitControls
        enablePan={false}
        enableZoom={false}
        enableRotate={true}
        autoRotate
        autoRotateSpeed={0.5}
        maxPolarAngle={Math.PI / 2}
        minPolarAngle={Math.PI / 2}
      />
    </>
  )
}

export default Scene3D
